import json
import pandas as pd
import re
import asyncio
import google.generativeai as genai
from typing import Dict, Any
import logging
from app.v1.api.techpana.utilities.content import ArticleContent

# Setup logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
logger = logging.getLogger(__name__)

class NewsSegmenter:
    def __init__(self, df: pd.DataFrame, api_key: str):
        self.df = df
        self.api_key = api_key
        logger.info("Initializing Gemini model...")
        genai.configure(api_key=self.api_key)
        try:
            self.model = genai.GenerativeModel("models/gemini-2.0-flash")
            logger.info("Model initialized successfully.")
        except Exception as e:
            logger.error(f"Failed to initialize GenerativeModel: {e}")
            raise

    def format_prompt(self, content: str, user_prompt: str) -> str:
        return (
            f"{user_prompt.strip()}\n\n"
            f"Content:\n{content.strip()}\n\n"
            f"Return the result in this JSON format:\n"
            "{\n"
            '  "headline": "...",\n'
            '  "lead": "...",\n'
            '  "context": "...",\n'
            '  "main_story": "...",\n'
            '  "quotes": "...",\n'
            '  "reaction": "...",\n'
            '  "conclusion_or_call_to_action": "...",\n'
            "}"
        )

    def parse_response(self, response_text: str) -> Dict[str, Any]:
        try:
            return json.loads(response_text)
        except json.JSONDecodeError:
            try:
                match = re.search(r"```(?:json)?(.*?)```", response_text, re.DOTALL)
                if match:
                    return json.loads(match.group(1).strip())
                match = re.search(r"(\{.*\})", response_text, re.DOTALL)
                if match:
                    return json.loads(match.group(1).strip())
                raise ValueError("No valid JSON found")
            except Exception as e:
                logger.warning(f"Failed to parse JSON: {e}")
                return {}

    async def process_row(self, i: int, row: pd.Series, user_prompt: str) -> Dict[str, Any]:
        prompt = self.format_prompt(row.get("clean_content", ""), user_prompt)

        try:
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            parsed = self.parse_response(response.text)
        except Exception as e:
            logger.error(f"Error in LLM call for row {i}: {e}")
            parsed = {}

        parsed_data = {
            "headline": parsed.get("headline", ""),
            "lead": parsed.get("lead", ""),
            "context": parsed.get("context", ""),
            "main_story": parsed.get("main_story", ""),
            "quotes": parsed.get("quotes", ""),
            "reaction": parsed.get("reaction", ""),
            "conclusion_or_call_to_action": parsed.get("conclusion_or_call_to_action", ""),
        }

        try:
            article = ArticleContent(**parsed_data)
            return article.to_dict()
        except Exception as e:
            logger.error(f"Error creating ArticleContent for row {i}: {e}")
            return parsed_data

    async def segment(self, user_prompt: str) -> pd.DataFrame:
        logger.info("Starting async segmentation...")

        tasks = [
            self.process_row(i, row, user_prompt)
            for i, row in self.df.iterrows()
        ]
        segmented_contents = await asyncio.gather(*tasks)

        self.df["segmented_content"] = segmented_contents
        logger.info("Segmentation complete.")
        return self.df.reset_index(drop=True)
