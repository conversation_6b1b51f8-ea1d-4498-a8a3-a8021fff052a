from pymongo import AsyncMongoClient
import time
from fastapi import HTT<PERSON>Ex<PERSON>
from typing import Dict, Any
from app.v1.api.techpana.rag.retrievers.retriever import TraditionalRetriever
from app.v1.api.techpana.utilities.hasher import PostHasher
import logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class ResponseGenerator:
    def __init__(self, mongo_uri: str, mongo_db: str, mongo_collection: str):
        self.mongo_uri = mongo_uri
        self.mongo_db = mongo_db
        self.mongo_collection = mongo_collection
        

        try:
            self.traditional_retriever = TraditionalRetriever()
        except Exception as e:
            logger.error(f"Failed to initialize ResponseGenerator: {str(e)}")
            raise

    async def traditional_retrieve_full(self, post_id: str) -> Dict[str, Any]:
        """
        Wrapper to invoke TraditionalRetriever with external MongoDB params.
        """
        try:
            result = await self.traditional_retriever.retrieve_full(
                post_id=post_id,
                mongo_uri=self.mongo_uri,
                mongo_db=self.mongo_db,
                mongo_collection=self.mongo_collection
            )
            result["success"] = True  # ✅ explicitly mark success
            return result

        except Exception as e:
            logger.error(f"❌ Error in traditional_retrieve_full for post_id={post_id}: {e}", exc_info=True)
            return {
                "success": False,
                "post_id": post_id,
                "error": "Internal error during traditional retrieval.",
                "main_post_summary": "",
                "related_posts": [],
                "token_usage": {}
            }


    async def generate_from_post_id(self, post_id: str) -> dict:
        start_time = time.time()

        result = await self.traditional_retrieve_full(post_id=post_id)
        elapsed_time = time.time() - start_time

        if result.get("success", False):
            return {
                "post_id": post_id,
                "main_post_summary": result.get("main_post_summary", "No summary generated"),
                "related_posts": result.get("related_posts", []),
                "token_usage": result.get("token_usage", {}),
                "retrieval_time": elapsed_time,
                "success": True
            }
        else:
            logger.warning(f"⚠️ generate_from_post_id failed for {post_id}: {result.get('error')}")
            return {
                "post_id": post_id,
                "error": result.get("error", "Unknown error"),
                "retrieval_time": elapsed_time,
                "success": False
            }


    async def get_report_from_post_id(self, post_id: str) -> dict:
        try:
            async with AsyncMongoClient(self.mongo_uri) as client:
                collection = client[self.mongo_db][self.mongo_collection]

                hashed_post_id = PostHasher.hash_post_id(post_id)

                document = await collection.find_one(
                    {"post_id": hashed_post_id},
                    {"_id": 0, "main_post_summary": 1, "related_posts": 1}
                )

                if not document:
                    raise HTTPException(
                        status_code=404,
                        detail=f"🔍 Post with post_id '{post_id}' not found in report collection."
                    )

                # ✅ Inject original post_id for reference
                document["post_id"] = post_id

                # ✅ Attempt to extract related post_id from post_url
                for related in document.get("related_posts", []):
                    url = related.get("post_url", "")
                    try:
                        related["post_id"] = url.rstrip("/").split("/")[-1] if url else None
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to parse related post_id from url: {url} — {e}")
                        related["post_id"] = None

                return document
                

        except HTTPException:
            raise  # Let FastAPI propagate intended HTTP errors
        except Exception as e:
            logger.error(f"❌ Unexpected error in get_report_from_post_id for post_id={post_id}: {e}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail="🚨 Failed to retrieve report due to server error."
            )

