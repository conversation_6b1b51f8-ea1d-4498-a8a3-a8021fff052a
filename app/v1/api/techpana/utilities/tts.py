import requests
import logging
from typing import Optional, Dict, Any
import asyncio
from io import BytesIO

logger = logging.getLogger(__name__)


class NepaliTTSService:
    """
    Service class for generating Nepali audio from text using TTS API.
    """
    
    def __init__(self, tts_url: str = "http://172.16.16.156:5000/tts"):
        """
        Initialize the TTS service.
        
        Args:
            tts_url: The URL of the TTS service endpoint
        """
        self.tts_url = tts_url
        self.default_headers = {
            'accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    
    def generate_audio(
        self,
        text: str,
        gender: str = 'ne_female',
        speaker: str = '0',
        length_scale: str = '1',
        noise_scale: str = '0.667',
        noise_w: str = '0.8',
        sentence_silence: str = '0.3'
    ) -> Optional[bytes]:
        """
        Generate Nepali audio from text synchronously.
        
        Args:
            text: The Nepali text to convert to speech
            gender: Voice gender/model to use (default: 'ne_finetuned_girl_6')
            speaker: Speaker ID (default: '0')
            length_scale: Speech speed control (default: '1')
            noise_scale: Noise level control (default: '0.667')
            noise_w: Noise width control (default: '0.8')
            sentence_silence: Silence between sentences (default: '0.3')
        
        Returns:
            Audio data as bytes if successful, None if failed
        """
        try:
            data = {
                'text': text,
                'gender': gender,
                'speaker': speaker,
                'length_scale': length_scale,
                'noise_scale': noise_scale,
                'noise_w': noise_w,
                'sentence_silence': sentence_silence,
            }
            
            logger.info(f"Generating audio for text: {text[:50]}...")
            response = requests.post(self.tts_url, headers=self.default_headers, data=data)
            
            if response.status_code == 200:
                logger.info("Audio generation successful")
                return response.content
            else:
                logger.error(f"TTS API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating audio: {e}", exc_info=True)
            return None
    
    async def generate_audio_async(
        self,
        text: str,
        gender: str = 'ne_female',
        speaker: str = '0',
        length_scale: str = '1',
        noise_scale: str = '0.667',
        noise_w: str = '0.8',
        sentence_silence: str = '0.3'
    ) -> Optional[bytes]:
        """
        Generate Nepali audio from text asynchronously.
        
        Args:
            text: The Nepali text to convert to speech
            gender: Voice gender/model to use (default: 'ne_finetuned_girl_6')
            speaker: Speaker ID (default: '0')
            length_scale: Speech speed control (default: '1')
            noise_scale: Noise level control (default: '0.667')
            noise_w: Noise width control (default: '0.8')
            sentence_silence: Silence between sentences (default: '0.3')
        
        Returns:
            Audio data as bytes if successful, None if failed
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.generate_audio,
            text, gender, speaker, length_scale, noise_scale, noise_w, sentence_silence
        )
    
    def generate_audio_from_summary(self, summary: str) -> Optional[bytes]:
        """
        Generate audio from a summary text, optimized for bullet points.
        
        Args:
            summary: The summary text (typically with bullet points)
        
        Returns:
            Audio data as bytes if successful, None if failed
        """
        # Clean up bullet points for better speech synthesis
        cleaned_text = summary.replace('•', '').strip()
        # Add pauses between bullet points
        cleaned_text = cleaned_text.replace('\n', '। ')
        
        return self.generate_audio(
            text=cleaned_text,
            sentence_silence='0.5'  # Longer pause for bullet points
        )
    
    async def generate_audio_from_summary_async(self, summary: str) -> Optional[bytes]:
        """
        Generate audio from a summary text asynchronously, optimized for bullet points.
        
        Args:
            summary: The summary text (typically with bullet points)
        
        Returns:
            Audio data as bytes if successful, None if failed
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate_audio_from_summary, summary)


# Convenience functions for easy usage
def generate_nepali_audio(text: str, **kwargs) -> Optional[bytes]:
    """
    Convenience function to generate Nepali audio from text.
    
    Args:
        text: The Nepali text to convert to speech
        **kwargs: Additional parameters for TTS generation
    
    Returns:
        Audio data as bytes if successful, None if failed
    """
    tts_service = NepaliTTSService()
    return tts_service.generate_audio(text, **kwargs)


async def generate_nepali_audio_async(text: str, **kwargs) -> Optional[bytes]:
    """
    Convenience function to generate Nepali audio from text asynchronously.

    Args:
        text: The Nepali text to convert to speech
        **kwargs: Additional parameters for TTS generation

    Returns:
        Audio data as bytes if successful, None if failed
    """
    tts_service = NepaliTTSService()
    return await tts_service.generate_audio_async(text, **kwargs)


class TTSError(Exception):
    """Custom exception for TTS-related errors."""
    pass


def save_audio_to_file(audio_data: bytes, filename: str) -> bool:
    """
    Save audio data to a file.

    Args:
        audio_data: The audio data as bytes
        filename: The filename to save to

    Returns:
        True if successful, False otherwise
    """
    try:
        with open(filename, 'wb') as f:
            f.write(audio_data)
        logger.info(f"Audio saved to {filename}")
        return True
    except Exception as e:
        logger.error(f"Error saving audio to {filename}: {e}")
        return False


def validate_text_length(text: str, max_length: int = 1000) -> bool:
    """
    Validate text length for TTS processing.

    Args:
        text: The text to validate
        max_length: Maximum allowed length

    Returns:
        True if valid, False otherwise
    """
    if not text or not text.strip():
        logger.warning("Empty text provided for TTS")
        return False

    if len(text) > max_length:
        logger.warning(f"Text too long for TTS: {len(text)} > {max_length}")
        return False

    return True


# Example usage function
def example_usage():
    """
    Example of how to use the TTS service.
    """
    sample_text = "नेपालमा मोबाइल डेटाको औसत डाउनलोड र अपलोड स्पिड घटेर विश्व वरियता खस्किएको छ।"

    # Synchronous usage
    tts_service = NepaliTTSService()
    audio_data = tts_service.generate_audio(sample_text)

    if audio_data:
        save_audio_to_file(audio_data, "sample_audio.wav")
        print("Audio generated and saved successfully!")
    else:
        print("Failed to generate audio")


if __name__ == "__main__":
    example_usage()
