"""
Example script demonstrating how to use the Nepali TTS service
with the existing summary generation functionality.
"""

import asyncio
import logging
from app.v1.api.techpana.utilities.tts import NepaliTTSService, generate_nepali_audio_async

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_tts_with_summary():
    """
    Test the TTS service with a sample Nepali summary.
    """
    # Sample summary text (similar to what's generated by the system)
    sample_summary = """• नेपालमा मोबाइल डेटाको औसत डाउनलोड र अपलोड स्पिड घटेको छ
• ग्राहक विस्तारको अनुपातमा सेवा प्रदायकको नेटवर्क क्षमता नबढ्नु मुख्य कारण हो
• फ्रिक्वेन्सी वितरण सम्बन्धी नीतिगत निर्णयहरू अड्किँदा राज्यलाई राजस्व गुमेको छ"""
    
    print("Testing TTS with sample summary...")
    print(f"Text: {sample_summary}")
    
    # Initialize TTS service
    tts_service = NepaliTTSService()
    
    # Generate audio from summary (optimized for bullet points)
    audio_data = await tts_service.generate_audio_from_summary_async(sample_summary)
    
    if audio_data:
        # Save the audio file
        filename = "sample_summary_audio.wav"
        with open(filename, 'wb') as f:
            f.write(audio_data)
        print(f"✅ Audio generated successfully! Saved as {filename}")
        print(f"Audio size: {len(audio_data)} bytes")
    else:
        print("❌ Failed to generate audio")


async def test_tts_with_custom_text():
    """
    Test the TTS service with custom Nepali text.
    """
    custom_text = "फास्ट चार्जिङले फोन छिटो चार्ज गर्न सहयोग गर्छ तर यसले ब्याट्रीको आयुमा असर पार्न सक्छ।"
    
    print("\nTesting TTS with custom text...")
    print(f"Text: {custom_text}")
    
    # Use the convenience function
    audio_data = await generate_nepali_audio_async(
        text=custom_text,
        length_scale='0.9',  # Slightly faster speech
        sentence_silence='0.4'  # Slightly longer pauses
    )
    
    if audio_data:
        filename = "custom_text_audio.wav"
        with open(filename, 'wb') as f:
            f.write(audio_data)
        print(f"✅ Custom audio generated! Saved as {filename}")
    else:
        print("❌ Failed to generate custom audio")


def demonstrate_integration_with_response_generator():
    """
    Show how to integrate TTS with the existing ResponseGenerator class.
    """
    print("\n" + "="*50)
    print("INTEGRATION EXAMPLE")
    print("="*50)
    
    integration_code = '''
# Example of how to add TTS to ResponseGenerator class:

from app.v1.api.techpana.utilities.tts import NepaliTTSService

class ResponseGenerator:
    def __init__(self, mongo_uri: str, mongo_db: str, mongo_collection: str):
        # ... existing initialization ...
        self.tts_service = NepaliTTSService()  # Add TTS service
    
    async def get_report_with_audio(self, post_id: str) -> dict:
        """
        Get report with optional audio generation for the summary.
        """
        # Get the regular report
        report = await self.get_report_from_post_id(post_id)
        
        # Generate audio for the main summary
        if "main_post_summary" in report:
            audio_data = await self.tts_service.generate_audio_from_summary_async(
                report["main_post_summary"]
            )
            
            if audio_data:
                # You could save to file, encode as base64, or return as bytes
                import base64
                report["audio_base64"] = base64.b64encode(audio_data).decode('utf-8')
                report["has_audio"] = True
            else:
                report["has_audio"] = False
        
        return report

# New API endpoint example:
@router.get("/get_report_with_audio/{post_id}")
async def get_report_with_audio(post_id: str):
    return await shared_state.generator.get_report_with_audio(post_id)
'''
    
    print(integration_code)


async def main():
    """
    Main function to run all tests.
    """
    print("🎵 Nepali TTS Service Test Suite")
    print("="*40)
    
    try:
        await test_tts_with_summary()
        await test_tts_with_custom_text()
        demonstrate_integration_with_response_generator()
        
        print("\n✅ All tests completed!")
        
    except Exception as e:
        logger.error(f"Error during testing: {e}", exc_info=True)
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
