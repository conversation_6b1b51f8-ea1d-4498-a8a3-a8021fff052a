import logging
from fastapi import HTTPException
from qdrant_client.http import models
import pandas as pd
from typing import Dict
from pymongo import AsyncMongoClient
from app.v1.api.techpana.rag.pipeline.segmentation import NewsSegmenter
from app.v1.api.techpana.rag.pipeline.preprocessor import PreProcessor
from app.v1.api.techpana.utilities.categoricalnewsbuilder import CategoricalNewsBuilder
from app.v1.api.techpana.core import shared_state
from app.v1.api.techpana.utilities.hasher import PostHasher
from app.v1.api.techpana.rag.vector_store import VectorStoreManager

logger = logging.getLogger(__name__)

class ResponsePopulate:
    def __init__(self):
        rag_config = shared_state.rag_config
        self.client = AsyncMongoClient(rag_config.mongo_uri)
        self.collection = self.client[rag_config.mongo_db_name][rag_config.mongo_collection_name]
        self.generator = shared_state.generator

    async def generate_qa_and_update(self, post_id: str):
        try:
            result = await self.generator.generate_from_post_id(post_id=post_id)

            if result.get("success", False):
                update_fields = {
                    "main_post_summary": result.get("main_post_summary"),
                    "related_posts": result.get("related_posts"),
                    "token_usage": result.get("token_usage"),
                    "updated": True
                }

                await self.collection.update_one(
                    {"post_id": post_id},
                    {"$set": update_fields}
                )

                logger.info(f"✅ QA + Summary updated for post_id={post_id} ⏱ {result.get('retrieval_time'):.2f}s")
            else:
                logger.warning(f"⚠️ QA generation skipped for post_id={post_id}: {result.get('error')}")
                # Do not overwrite `updated` field here

        except Exception as e:
            logger.error(f"❌ Unhandled exception in generate_qa_and_update for post_id={post_id}: {e}", exc_info=True)
            # Still avoid changing `updated` — log but don’t persist QA failure



    async def process_incoming_post(self, post_data: Dict):
        try:
            post_id = PostHasher.hash_post_id(str(post_data.get("id")))
            rag_config = shared_state.rag_config

            if not rag_config:
                raise RuntimeError("❌ RAG config not initialized.")

            # 1️⃣ Preprocess
            df = pd.DataFrame([post_data])
            processed_df = await PreProcessor(df).preprocess()

            # 2️⃣ Segment
            segmenter = NewsSegmenter(df=processed_df, api_key=rag_config.gemini_api_key)
            segmented_df = await segmenter.segment(
                user_prompt="You are a news article segmentation expert. Segment the content "
                            "into headline, lead, context, main story, quotes, reaction, and conclusion/call to action."
            )

            # 3️⃣ Build structured data
            structured_news_list = await CategoricalNewsBuilder(segmented_df).build()

            # 4️⃣ Add index
            max_doc = await self.collection.find_one(sort=[("index", -1)])
            base_index = max_doc["index"] if max_doc and "index" in max_doc else 0
            for i, item in enumerate(structured_news_list):
                item["index"] = base_index + i + 1

            # 5️⃣ Insert into MongoDB
            await self.collection.insert_many(structured_news_list)
            logger.info(f"✅ Inserted {len(structured_news_list)} chunks for post_id={post_id}")

            # 6️⃣ Embed
            vector_manager = VectorStoreManager()
            post_ids = [item["post_id"] for item in structured_news_list]
            try:
                success, _ = await vector_manager.setup_and_populate(post_ids)
                if success:
                    await self.collection.update_many(
                        {"post_id": {"$in": post_ids}},
                        {"$set": {"embedded": True}}
                    )
                    logger.info("✅ Embedding successful.")
                else:
                    logger.warning("⚠️ Embedding failed.")
            except Exception as e:
                logger.error(f"❌ Embedding error: {e}", exc_info=True)

            # 7️⃣ Generate QA + Summary
            await self.generate_qa_and_update(post_id)

            return {
                "status": "processed",
                "post_id": post_id,
                "message": "Post processed, embedded, and QA generated.",
                "chunks_saved": len(structured_news_list)
            }

        except Exception as e:
            logger.error(f"❌ Failed processing post: {e}", exc_info=True)
            return {
                "status": "error",
                "post_id": post_data.get("id"),
                "message": str(e)
            }

    async def process_all_documents_and_update(self):
        try:
            docs = await self.collection.find({}).to_list(length=None)
            logger.info(f"📄 Total documents: {len(docs)}")

            for i, doc in enumerate(docs):
                post_id = doc.get("post_id")
                if not post_id:
                    logger.warning(f"⚠️ Skipping index {i} (no post_id)")
                    continue

                if doc.get("main_post_summary") and doc.get("related_posts") and doc.get("updated"):
                    logger.info(f"⏩ Skipping post_id={post_id}: already processed")
                    continue

                logger.info(f"🔄 [{i+1}/{len(docs)}] Processing post_id={post_id}")
                await self.generate_qa_and_update(post_id)

            logger.info("✅ Completed batch QA + summary generation.")
        except Exception as e:
            logger.error(f"❌ Batch processing failed: {e}", exc_info=True)


    async def check_post(self, update_all: bool = False):
        if update_all:
            await self.process_all_documents_and_update()


    async def process_post_and_generate(self, post_data: Dict):
        return await self.process_incoming_post(post_data)


    async def delete_post_by_id(self, post_id: str) -> dict:
        """
        Deletes a single post document from MongoDB and all associated vectors from Qdrant.
        """
        hashed_post_id = PostHasher.hash_post_id(post_id)
        vector_manager = VectorStoreManager()

        # Delete a single doc from MongoDB
        mongo_result = await self.collection.delete_one({"post_id": hashed_post_id})

        # # If post doesn't exist in Mongo
        if mongo_result.deleted_count == 0:
            raise HTTPException(status_code=404, detail=f"Post {post_id} not found in MongoDB.")

        # Delete vectors from Qdrant
        qdrant_status = "failed"
        deleted_count = 0

        try:
            # Step 1: Fetch matching vector points
            scroll_response = vector_manager.client.scroll(
                collection_name=vector_manager.collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="ref_post_id",
                            match=models.MatchValue(value=hashed_post_id)
                        )
                    ]
                ),
                limit=1000
            )
            points = scroll_response[0]
            deleted_count = len(points)

            if deleted_count > 0:
                logger.info(f"🧠 Found {deleted_count} Qdrant points for post_id={post_id}")

                # Step 2: Delete matching points
                vector_manager.client.delete(
                    collection_name=vector_manager.collection_name,
                    points_selector=models.FilterSelector(
                        filter=models.Filter(
                            must=[
                                models.FieldCondition(
                                    key="ref_post_id",
                                    match=models.MatchValue(value=hashed_post_id)
                                )
                            ]
                        )
                    )
                )

                # Step 3: Verify deletion
                post_delete_check = vector_manager.client.scroll(
                    collection_name=vector_manager.collection_name,
                    scroll_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="metadata.ref_post_id",
                                match=models.MatchValue(value=hashed_post_id)
                            )
                        ]
                    ),
                    limit=100
                )
                remaining = post_delete_check[0]
                if len(remaining) == 0:
                    qdrant_status = "deleted"
                    logger.info(f"✅ Qdrant vectors deleted for post_id={post_id}")
                else:
                    qdrant_status = "partial_or_failed"
                    logger.warning(f"⚠️ Some Qdrant vectors still remain for post_id={post_id}")

            else:
                qdrant_status = "no_points_found"
                logger.info(f"ℹ️ No Qdrant vectors found for post_id={post_id}")

        except Exception as e:
            logger.error(f"❌ Qdrant deletion error for post_id={post_id}: {str(e)}")

        return {
            "post_id": post_id,
            "mongo_status": "deleted",
            "qdrant_status": qdrant_status,
            "qdrant_deleted_count": deleted_count
        }
