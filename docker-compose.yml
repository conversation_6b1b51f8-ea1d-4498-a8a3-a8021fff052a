version: '3.8'

services:
  app:
    build: .
    image: techpana:latest
    expose:
      - "8000"        # internal port, no host binding
    networks:
      - internal_net
    env_file:
      - .env
    volumes:
      # Mount log directory for real-time log writing
      - /var/log/widget_techpaana:/app/logs:rw
      # Create a named volume for persistent logs
      - widget_logs:/app/logs
    deploy:
      replicas: 3
    labels:
      - "com.docker.compose.project=techpana-backend"  # ← ADD THIS
    # Ensure log directory exists and has proper permissions
    user: "0:0"  # Run as root to ensure log directory access
    environment:
      - LOG_LEVEL=INFO
      - LOG_TO_FILE=true
      - LOG_DIR=/app/logs

  haproxy:
    image: haproxy:alpine
    container_name: haproxy_lb
    ports:
      - "8010:80"     # expose host port 8010 → container port 80 (HAProxy)
    volumes:
      - ./haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
    networks:
      - internal_net
    labels:
      - "com.docker.compose.project=techpana-backend"  # ← ADD THIS

volumes:
  # Named volume for persistent log storage
  widget_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/log/widget_techpaana

networks:
  internal_net:
    driver: bridge